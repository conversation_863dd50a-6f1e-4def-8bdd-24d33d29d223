import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, Zap, Globe } from 'lucide-react';
import TypewriterText from './TypewriterText';

interface StreamingData {
  type: 'status' | 'translation_chunk';
  message?: string;
  progress: number;
  batch_number?: number;
  total_batches?: number;
  text?: string;
}

interface StreamingTranslationViewerProps {
  isStreaming: boolean;
  streamingData: StreamingData | null;
  accumulatedText: string;
  onStreamingComplete?: () => void;
}

const StreamingTranslationViewer: React.FC<StreamingTranslationViewerProps> = ({
  isStreaming,
  streamingData,
  accumulatedText,
  onStreamingComplete
}) => {
  const [currentBatchText, setCurrentBatchText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new content is added
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [accumulatedText, currentBatchText]);

  useEffect(() => {
    if (streamingData?.type === 'translation_chunk' && streamingData.text) {
      setCurrentBatchText(streamingData.text);
      setIsTyping(true);
    }
  }, [streamingData]);

  const handleTypewriterComplete = () => {
    setIsTyping(false);
    // Notify parent that this batch is complete
    onStreamingComplete?.();
  };

  if (!isStreaming) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Progress Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Sparkles className="h-6 w-6 text-cyan-400 animate-pulse" />
            <div className="absolute -inset-2 bg-cyan-400/20 rounded-full blur-lg animate-pulse"></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-cyan-400">AI Translation in Progress</h3>
            <p className="text-sm text-slate-400">
              {streamingData?.message || 'Processing your document...'}
            </p>
          </div>
        </div>
        
        {streamingData?.batch_number && streamingData?.total_batches && (
          <div className="text-right">
            <div className="text-sm text-slate-300">
              Batch {streamingData.batch_number} of {streamingData.total_batches}
            </div>
            <div className="text-xs text-slate-400">
              {Math.round((streamingData.batch_number / streamingData.total_batches) * 100)}% Complete
            </div>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-slate-700 rounded-full h-3 overflow-hidden">
        <div 
          className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full transition-all duration-500 ease-out"
          style={{ width: `${streamingData?.progress || 0}%` }}
        />
      </div>

      {/* Streaming Text Display */}
      <div 
        ref={scrollRef}
        className="bg-slate-900/50 rounded-lg p-4 border border-slate-700 h-[calc(100vh-280px)] overflow-auto"
      >
        <div className="space-y-4">
          {/* Status Messages */}
          {streamingData?.type === 'status' && (
            <div className="flex items-center gap-2 text-cyan-400 mb-4">
              <Globe className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">{streamingData.message}</span>
            </div>
          )}

          {/* Accumulated Text (already typed) */}
          {accumulatedText && (
            <div className="text-slate-200 leading-relaxed whitespace-pre-wrap">
              {accumulatedText}
            </div>
          )}

          {/* Current Batch Being Typed */}
          {streamingData?.type === 'translation_chunk' && currentBatchText && (
            <div className="border-t border-slate-600 pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-4 w-4 text-emerald-400" />
                <span className="text-sm text-emerald-400 font-medium">
                  Translating Batch {streamingData.batch_number}...
                </span>
              </div>
              <TypewriterText
                text={currentBatchText}
                speed={80} // Faster typing for better UX
                onComplete={handleTypewriterComplete}
                className="text-slate-200 leading-relaxed whitespace-pre-wrap"
              />
            </div>
          )}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-slate-400 mt-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-xs">AI is typing...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StreamingTranslationViewer;
