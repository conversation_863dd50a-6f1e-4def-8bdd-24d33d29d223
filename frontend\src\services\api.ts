const API_BASE_URL = 'http://10.164.0.149:8000';
export interface UploadResponse {
  message: string;
  filename: string;
  file_path: string;
}

export interface TranslationResponse {
  message: string;
  translated_content: string;
  output_file: string;
}

export interface FeaturesResponse {
  message: string;
  markdown: string;
}

export interface ScopeResponse {
  message: string;
  markdown: string;
}

export interface FileInfo {
  filename: string;
  size: number;
  uploaded_at: number;
}

export interface FilesResponse {
  files: FileInfo[];
}

class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  async uploadPdf(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseUrl}/upload-pdf`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    return response.json();
  }

  async translatePdf(filename: string): Promise<TranslationResponse> {
    const response = await fetch(`${this.baseUrl}/translate-pdf?filename=${encodeURIComponent(filename)}`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error(`Translation failed: ${response.statusText}`);
    }

    return response.json();
  }

  async extractFeatures(filename: string): Promise<FeaturesResponse> {
    const response = await fetch(`${this.baseUrl}/analyze-features?filename=${encodeURIComponent(filename)}`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error(`Feature extraction failed: ${response.statusText}`);
    }

    return response.json();
  }

  async extractScope(filename: string): Promise<ScopeResponse> {
    const response = await fetch(`${this.baseUrl}/analyze-scope?filename=${encodeURIComponent(filename)}`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error(`Scope extraction failed: ${response.statusText}`);
    }

    return response.json();
  }

  async listFiles(): Promise<FilesResponse> {
    const response = await fetch(`${this.baseUrl}/files`);

    if (!response.ok) {
      throw new Error(`Failed to list files: ${response.statusText}`);
    }

    return response.json();
  }

  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/`);
      return response.ok;
    } catch {
      return false;
    }
  }

  // Streaming translation with Server-Sent Events
  translatePdfStream(filename: string, onProgress: (data: any) => void, onComplete: (content: string) => void, onError: (error: string) => void): EventSource {
    // Use the improved streaming endpoint for better cross-platform compatibility
    const eventSource = new EventSource(`${this.baseUrl}/translate-pdf-stream-v2?filename=${encodeURIComponent(filename)}`);

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'status':
            onProgress({
              type: 'status',
              message: data.message,
              progress: data.progress
            });
            break;

          case 'translation_chunk':
            onProgress({
              type: 'translation_chunk',
              batch_number: data.batch_number,
              total_batches: data.total_batches,
              text: data.text,
              progress: data.progress
            });
            break;

          case 'final_content':
            onComplete(data.content);
            eventSource.close();
            break;

          case 'error':
            onError(data.message);
            eventSource.close();
            break;

          case 'keepalive':
            // Ignore keepalive messages - they're just to prevent connection timeout
            console.debug('Received keepalive from server');
            break;

          default:
            console.warn('Unknown message type:', data.type);
            break;
        }
      } catch (error) {
        console.error('Failed to parse streaming data:', error);
        onError('Failed to parse streaming data');
        eventSource.close();
      }
    };

    eventSource.onerror = (error) => {
      console.error('EventSource failed:', error);

      // Check if the connection was closed normally
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('EventSource connection closed normally');
        return;
      }

      // Only report error if it's an actual connection issue
      if (eventSource.readyState === EventSource.CONNECTING) {
        onError('Reconnecting to server...');
      } else {
        onError('Connection to server lost');
        eventSource.close();
      }
    };

    // Return the EventSource for potential cancellation
    return eventSource;
  }

  // Fallback method using the original endpoint
  translatePdfStreamFallback(filename: string, onProgress: (data: any) => void, onComplete: (content: string) => void, onError: (error: string) => void): EventSource {
    // Use the original streaming endpoint as fallback
    const eventSource = new EventSource(`${this.baseUrl}/translate-pdf-stream?filename=${encodeURIComponent(filename)}`);

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'status':
            onProgress({
              type: 'status',
              message: data.message,
              progress: data.progress
            });
            break;

          case 'translation_chunk':
            onProgress({
              type: 'translation_chunk',
              batch_number: data.batch_number,
              total_batches: data.total_batches,
              text: data.text,
              progress: data.progress
            });
            break;

          case 'final_content':
            onComplete(data.content);
            eventSource.close();
            break;

          case 'error':
            onError(data.message);
            eventSource.close();
            break;
        }
      } catch (error) {
        onError('Failed to parse streaming data');
        eventSource.close();
      }
    };

    eventSource.onerror = (error) => {
      console.error('EventSource failed:', error);
      onError('Connection to server lost');
      eventSource.close();
    };

    return eventSource;
  }
}

export const apiService = new ApiService();
export default apiService; 